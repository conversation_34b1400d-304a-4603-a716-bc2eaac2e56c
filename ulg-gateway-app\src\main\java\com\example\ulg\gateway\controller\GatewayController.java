package com.example.ulg.gateway.controller;

import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import com.example.ulg.gateway.service.GatewayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
public class GatewayController {

    private final GatewayService gatewayService;

    /**
     * 统一的聊天补全接口，根据request.stream参数自动选择流式或非流式响应
     * @param request 标准的OpenAI请求体
     * @param response HTTP响应对象，用于设置Content-Type
     * @return 响应实体
     */
    @PostMapping(value = "/chat/completions")
    public Mono<ResponseEntity<?>> chatCompletions(@RequestBody ChatCompletionRequest request,
                                                   ServerHttpResponse response) {
        boolean isStream = request.getStream() != null && request.getStream();
        if (isStream) {
            // 设置SSE响应头
            response.getHeaders().set("Content-Type", MediaType.TEXT_EVENT_STREAM_VALUE);
            response.getHeaders().set("Cache-Control", "no-cache");
            response.getHeaders().set("Connection", "keep-alive");

            // 对于流式请求，返回Flux<String>
            Flux<String> stream = gatewayService.chatCompletionsStream(request)
                    .doOnError(e -> log.error("流式处理出错", e));
            return Mono.just(ResponseEntity.ok().body(stream));
        } else {
            // 对于非流式请求，返回Mono<ChatCompletionResult>
            return gatewayService.chatCompletions(request)
                    .doOnError(e -> log.error("非流式处理出错", e))
                    .map(result -> ResponseEntity.ok().body(result));
        }
    }

}
