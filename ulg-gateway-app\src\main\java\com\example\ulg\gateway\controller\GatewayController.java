package com.example.ulg.gateway.controller;

import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import com.example.ulg.gateway.service.GatewayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
public class GatewayController {

    private final GatewayService gatewayService;

    /**
     * 统一的聊天补全接口，同时支持流式和非流式响应。
     * @param request 标准的OpenAI请求体。
     * @return 如果 request.stream 为 true，返回 Flux<String> (SSE)，否则返回 Mono<ChatCompletionResult>。
     */
    @PostMapping(value = "/chat/completions")
    public ResponseEntity<?> chatCompletions(@RequestBody ChatCompletionRequest request) {

        boolean isStream = request.getStream() != null && request.getStream();
        if (isStream) {
            // 对于流式请求，返回一个Flux。通过使用ResponseEntity并显式设置Content-Type为
            // "text/event-stream"，我们告诉Spring WebFlux将此作为Server-Sent Event (SSE)流处理。
            // 这解决了当返回类型为Object时，框架无法正确推断响应类型的问题。
            Flux<String> stream = gatewayService.chatCompletionsStream(request)
                    .doOnError(e -> log.error("流式处理出错", e));
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_EVENT_STREAM)
                    .body(stream);
        } else {
            // 对于非流式请求，返回一个Mono。ResponseEntity会帮助Spring正确地处理它，
            // 将Mono的内容序列化为JSON响应体。
            Mono<ChatCompletionResult> result = gatewayService.chatCompletions(request)
                    .doOnError(e -> log.error("非流式处理出错", e));
            return ResponseEntity.ok(result);
        }
    }

}
