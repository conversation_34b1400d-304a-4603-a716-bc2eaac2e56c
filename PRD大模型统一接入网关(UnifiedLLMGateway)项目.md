# PRD: 大模型统一接入网关 (Unified LLM Gateway) 项目

---

### **PRD: 大模型统一接入网关 (Unified LLM Gateway) 项目**

|文档版本|**V3.0 (Deliverable)**|
| :---------| :---------------------------------------------------------------------------|
|**创建日期**|2023-10-27|
|**更新日期**|2023-10-28|
|**创建人/更新人**|[您的名字/项目经理]|
|**项目代号**|ULG (Unified LLM Gateway)|
|**状态**|**定稿**|
|**利益相关者**|产品经理、项目经理、架构师、后端开发团队、前端开发团队、测试团队、运维团队|

---

### 1. 项目概述 (Project Overview)

#### 1.1. 项目背景 (Background)

随着大语言模型（LLM）技术的飞速发展，市场上涌现出 OpenAI (ChatGPT), Google (Gemini), Anthropic (Claude) 等众多优秀的服务提供商。然而，这些提供商的API在请求/响应格式、认证方式、参数定义上各不相同，给开发者带来了巨大的挑战：

* **高昂的切换成本**：当业务需要从一个模型切换到另一个模型时，往往需要重写大量的API调用代码。
* **复杂的适配逻辑**：在同一个应用中集成多个模型时，开发者需要维护多套请求和响应处理逻辑，增加了代码的复杂性和维护难度。
* **管理不便**：不同服务商的密钥（API Key）、Base URL等配置信息分散，难以统一管理和轮换。

本项目旨在解决以上痛点，通过构建一个“大模型统一接入网关”，对外提供标准化的、与OpenAI API格式完全兼容的接口，并将其打造为一个**可观测、高韧性、可重用的核心适配平台**。

#### 1.2. 目标用户 (Target Audience)

* **应用开发者**：希望快速集成、灵活切换不同LLM服务的开发者。
* **中小型企业/团队**：需要统一管理LLM资源、降低开发和维护成本、控制API支出的团队。
* **AI服务集成商**：需要为客户提供稳定、多模型支持的解决方案的服务商。

#### 1.3. 项目目标 (Project Goals)

1. **核心目标**：实现一个稳定、高效的API网关，将OpenAI格式的API请求无缝适配到多种下游LLM服务提供商。
2. **产品目标**：提供一个简洁易用的管理后台，方便用户对LLM服务提供商进行全生命周期管理，并为运营提供数据支撑。
3. **技术目标**：采用Maven多模块化架构（Spring Boot + JPA + Vue），构建一个高内聚、低耦合、易于扩展的系统。核心适配逻辑将作为独立模块开发，确保其可被其他系统轻松复用。
4. **运营目标**：打造一个具备**可观测性、韧性与容错能力、成本与安全控制**的生产级服务，确保平台的长期稳定运行和健康发展。

---

### 2. 功能需求 (Functional Requirements)

#### FR-01: 核心模块：API适配网关

* **FR-01.01: OpenAI兼容接口**

  * **描述**: 系统必须提供与OpenAI官方API `/v1/chat/completions`​ 完全兼容的端点。
  * **验收标准**: 支持 `POST /v1/chat/completions`​ 请求；支持 `stream`​ 模式和非流式响应；请求头通过 `Authorization: Bearer <token>`​ 传递认证信息；请求体支持OpenAI `ChatCompletion`​ 的标准JSON结构。
* **FR-01.02: 请求格式转换**

  * **描述**: 网关接收到OpenAI格式的请求后，能根据路由规则将其转换为指定下游服务商（如Gemini）的API请求格式。
  * **验收标准**: 能正确解析 `model`​ 字段并映射到服务商；能将 `messages`​, `temperature`​, `max_tokens`​ 等参数正确适配；能正确构建发往下游的HTTP请求。
* **FR-01.03: 响应格式转换**

  * **描述**: 网关接收到下游服务商的响应后，能将其转换为标准的OpenAI API响应格式。
  * **验收标准**: 能将下游响应封装成OpenAI的 `ChatCompletion`​ 对象（非流式）或 `ChatCompletionChunk`​ 对象（流式）；能正确转换和透传错误信息。

#### FR-02: 后台管理模块：服务商管理

* **FR-02.01: 服务商列表查询**

  * **描述**: 用户可以在管理界面查看、搜索和分页浏览所有已录入的LLM服务提供商。
  * **验收标准**: 列表展示关键信息（名称、类型、模型名等）；支持按名称模糊搜索；支持分页。
* **FR-02.02: 新增/编辑/删除服务商**

  * **描述**: 用户可以通过图形化界面对服务商配置进行增、删、改操作。
  * **验收标准**: 提供表单用于录入/修改信息（提供商名称、类型、模型名称、API密钥、Base URL等）；API密钥在前端为密码框，在后端加密存储；删除操作有二次确认。

#### FR-03: 待办功能：用户登录与权限管理

* **描述**: 引入用户系统，只有登录用户才能访问管理后台。未来可扩展为多租户或基于角色的权限控制。

#### FR-04: 待办功能：系统级密钥管理

* **描述**: 为本网关系统生成和管理API密钥。外部应用调用网关时，使用本系统颁发的密钥进行认证，而非直接透传下游密钥。

#### FR-05: 综合可观测性 (Observability)

* **FR-05.01: 结构化日志**

  * **描述**: 所有日志输出为结构化的JSON格式，并包含唯一的`traceId`​以串联单次请求的完整链路。
  * **验收标准**: 日志包含`timestamp`​, `level`​, `traceId`​, `serviceName`​, `model_name`​, `provider_type`​, `request_latency_ms`​等关键字段。
* **FR-05.02: 关键指标监控**

  * **描述**: 系统通过标准端点（如`/actuator/prometheus`​）暴露关键性能指标（Metrics）。
  * **验收标准**: 暴露业务指标（请求总数、Token数）、性能指标（P99/P95延迟）、系统指标（JVM、连接池）和韧性指标（熔断状态）。
* **FR-05.03: 分布式追踪**

  * **描述**: 实现端到端的请求追踪，可视化API调用的完整生命周期。
  * **验收标准**: 集成OpenTelemetry，可在Jaeger/Zipkin等系统中查看请求调用链。

#### FR-06: 韧性与容错 (Resilience & Fault Tolerance)

* **FR-06.01: 超时与重试**

  * **描述**: 对下游LLM的API调用配置合理的超时，并对可恢复的错误进行自动重试。
  * **验收标准**: 可在服务商配置中指定超时时间；可配置重试次数和指数退避策略。
* **FR-06.02: 熔断器**

  * **描述**: 当某个下游服务商的错误率或慢调用比例超过阈值时，自动熔断，快速失败，防止雪崩。
  * **验收标准**: 集成Resilience4J，可配置熔断阈值和时长，熔断状态可监控。
* **FR-06.03: 备用模型/回退策略 (高级)**

  * **描述**: 允许为某个模型配置备用模型，当主模型调用失败时，自动尝试调用备用模型。
  * **验收标准**: 服务商配置中可关联一个`fallback_provider_id`​，系统能实现自动回退逻辑。

#### FR-07: 成本与安全控制 (Cost & Security Control)

* **FR-07.01: 请求计量**

  * **描述**: 详细记录每次API调用的Token用量和耗时等信息，为计费和审计提供数据基础。
  * **验收标准**: `request_log`​表被正确填充，记录`traceId`​, `provider_id`​, `prompt_tokens`​, `completion_tokens`​, `latency_ms`​等。
* **FR-07.02: 配额与速率限制 (高级)**

  * **描述**: 为系统颁发的每个API Key设置精细的请求速率限制（QPS/QPM）和总用量配额。
  * **验收标准**: 可为Key配置访问策略；超出限制的请求返回 `429 Too Many Requests`​。

---

### 3. 非功能性需求 (Non-Functional Requirements)

|类别|需求描述|
| :-----| :----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|**性能**|- API网关核心转换逻辑的额外延迟应控制在50ms以内。<br>- 系统初期应能支持至少100 QPS的并发请求。|
|**可扩展性**|- **模型扩展**: 增加新的服务商类型时，应只需在**核心适配模块**中实现新适配器，对其他模块无侵入。<br>- **数据库兼容**: 使用JPA，确保系统能无缝切换SQLite（开发）和MySQL/PostgreSQL（生产）。|
|**安全性**|- 所有存储在数据库中的敏感信息（如API密钥）必须进行强加密处理。<br>- 前后端通信及对外服务必须使用HTTPS。<br>- 防止常见的Web攻击，如SQL注入、XSS等。|
|**可用性**|- 系统核心服务应保证99.9%的可用性。<br>- 提供优雅的降级策略（如熔断、回退），当下游服务商不可用时，能保护自身并返回明确错误。|
|**可维护性/可重用性**|- **采用Maven多模块设计**，将Web层与核心业务逻辑完全隔离，降低模块间耦合度。<br>- **核心适配模块 (**​**​`ulg-adapter-core`​**​ **)**  设计为纯逻辑库，不依赖Web环境，可独立打包和被其他项目引用。<br>- 提供完善的、可被机器解析的结构化日志。|

---

### 4. 技术栈与架构 (Technology & Architecture)

#### 4.1. 技术栈

* **后端**: Spring Boot 3.x, Spring Web, Spring Data JPA, Spring Security, Lombok, **Micrometer, Resilience4J, OpenTelemetry SDK**。
* **数据库**: JPA + Hibernate。开发环境使用 H2/SQLite，生产环境推荐 MySQL 8.0+ 或 PostgreSQL。
* **前端**: Vue 3, Vite, Vue Router, Pinia, Axios, Element Plus。
* **构建工具**: **Maven (多模块项目)** , Node.js + pnpm/npm。
* **基础设施 (推荐)** : Prometheus & Grafana (监控), Jaeger (追踪), ELK Stack / Loki (日志)。

#### 4.2. 系统架构设计

本项目将采用Maven多模块架构，将系统清晰地划分为不同的职责单元。

* ​**​`ulg-adapter-core`​**​  **(核心适配模块)** : 一个可独立重用的纯Java库（JAR），包含所有适配器接口和实现，不依赖Spring框架。
* ​**​`ulg-gateway-app`​**​  **(网关应用模块)** : 一个可执行的Spring Boot应用，负责处理HTTP请求、数据库交互、业务逻辑编排，并调用`ulg-adapter-core`​。
* ​**​`ulg-admin-ui`​**​  **(前端管理界面)** : 独立的Vue前端项目，通过API与`ulg-gateway-app`​交互。

#### 4.3. 模块交互架构图

```mermaid
graph TD
    subgraph "外部用户/系统"
        A[客户端应用]
        G[管理员]
    end

    subgraph "ULG 项目"
        subgraph "ulg-gateway-app (Spring Boot)"
            B["Controller (Web层)"]
            S["Service & JPA (业务与数据层)"]
            F["ULG 数据库 (MySQL/SQLite)"]
        end

        subgraph "ulg-adapter-core (Java Library)"
            C["适配器工厂/管理器"]
            C1["Gemini Adapter"]
            C2["OpenAI Adapter"]
            C3["..."]
        end

        subgraph "ulg-admin-ui (Vue App)"
            E["管理界面"]
        end
    end

    subgraph "下游服务"
        D["外部LLM API (Gemini, etc.)"]
    end

    A -- "1. OpenAI格式请求" --> B
    B -- "2. 调用业务逻辑" --> S
    S -- "3. 获取Provider配置" --> F
    S -- "4. 调用核心适配器" --> C
    C -- "5. 选择具体适配器" --> C1
    C1 -- "6. 转换并请求" --> D
    D -- "7. 下游响应" --> C1
    C1 -- "8. 转换响应" --> S
    S -- "9. 返回给Web层" --> B
    B -- "10. OpenAI格式响应" --> A

    G -- "操作" --> E
    E -- "Admin API" <--> B
```

---

### 5. 数据模型设计 (Data Model)

#### 表 1: `llm_provider`​

|字段名 (Column)|类型 (Type)|约束 (Constraints)|描述 (Description)|
| :----------------| :------------| :-------------------| :--------------------------------------|
|​`id`​|​`BIGINT`​|主键, 自增|唯一标识符|
|​`provider_name`​|​`VARCHAR(100)`​|NOT NULL|提供商的自定义名称，用于UI展示|
|​`provider_type`​|​`VARCHAR(50)`​|NOT NULL|提供商类型 (e.g., 'GEMINI', 'OPENAI')|
|​`model_name`​|​`VARCHAR(100)`​|NOT NULL, UNIQUE|模型的唯一标识，用于API请求路由|
|​`api_key`​|​`VARCHAR(512)`​|NOT NULL|加密后的API密钥|
|​`base_url`​|​`VARCHAR(255)`​|NOT NULL|API的Base URL|
|​`timeout_ms`​|​`INT`​|NULL|请求超时时间（毫秒）|
|​`fallback_provider_id`​|​`BIGINT`​|NULL, FK(`llm_provider`​.`id`​)|备用提供商ID|
|​`notes`​|​`TEXT`​|NULL|备注信息|
|​`created_at`​|​`DATETIME`​|NOT NULL|创建时间|
|​`updated_at`​|​`DATETIME`​|NOT NULL|更新时间|

#### 表 2: `request_log`​

|字段名 (Column)|类型 (Type)|约束 (Constraints)|描述 (Description)|
| :----------------| :------------| :--------------------| :-----------------------------|
|​`id`​|​`BIGINT`​|主键, 自增|唯一标识符|
|​`trace_id`​|​`VARCHAR(100)`​|NOT NULL, INDEX|关联日志和追踪的唯一ID|
|​`api_key_id`​|​`BIGINT`​|NULL, FK|关联系统颁发的密钥ID（待办）|
|​`provider_id`​|​`BIGINT`​|NOT NULL, FK(`llm_provider`​.`id`​)|关联`llm_provider`​表|
|​`request_timestamp`​|​`DATETIME(3)`​|NOT NULL|请求到达网关的时间|
|​`is_success`​|​`BOOLEAN`​|NOT NULL|请求是否成功|
|​`http_status`​|​`INT`​|NULL|最终返回给用户的HTTP状态码|
|​`prompt_tokens`​|​`INT`​|NULL|输入token数|
|​`completion_tokens`​|​`INT`​|NULL|输出token数|
|​`total_tokens`​|​`INT`​|NULL|总token数|
|​`latency_ms`​|​`INT`​|NOT NULL|请求总耗时（毫秒）|

---

### 6. API 接口设计 (API Design)

#### 6.1. 网关核心API

* **Endpoint**: `POST /v1/chat/completions`​
* **描述**: 接收符合OpenAI规范的聊天请求。
* **请求头**: `Authorization: Bearer <token>`​, `Content-Type: application/json`​
* **请求体**: 标准OpenAI `ChatCompletion`​ JSON。
* **响应体**: 标准OpenAI `ChatCompletion`​ 或流式 `ChatCompletionChunk`​ JSON。

#### 6.2. 后台管理API

* **基路径**: `/api/admin/providers`​
* **认证**: 后续需由Spring Security保护

|方法|URL|描述|
| :-----| :-----| :---------------------------------|
|​`POST`​|​`/`​|新增一个服务商|
|​`GET`​|​`/`​|获取服务商列表（支持分页和搜索）|
|​`GET`​|​`/{id}`​|获取指定ID的服务商详情|
|​`PUT`​|​`/{id}`​|更新指定ID的服务商信息|
|​`DELETE`​|​`/{id}`​|删除指定ID的服务商|

---

### 7. 项目里程碑 (Roadmap)

* **Phase 1: MVP - 核心功能与基础可观测性 (预计5周)**

  * **目标**: 搭建多模块项目骨架，实现核心转换功能和基础后台，并具备基础的排错能力。
  * **任务**:

    * [ ] **架构**: 搭建Maven多模块项目结构 (`ulg-project`​, `ulg-adapter-core`​, `ulg-gateway-app`​)。
    * [ ] **核心模块 (**​**​`ulg-adapter-core`​**​ **)** : 实现`LlmAdapter`​接口和OpenAI-to-Gemini适配器（流式与非流式）。
    * [ ] **应用模块 (**​**​`ulg-gateway-app`​**​ **)** : 实现服务商CRUD接口、`/v1/chat/completions`​端点。
    * [ ] **前端模块 (**​**​`ulg-admin-ui`​**​ **)** : 完成服务商管理的增、删、改、查界面。
    * [ ] **可观测性**: 实现结构化日志，暴露基础请求计数和延迟指标。
    * [ ] **韧性**: 为下游调用添加可配置的超时。
    * [ ] **测试**: 完成端到端的调用流程测试。
* **Phase 2: 健壮性与安全增强 (预计3周)**

  * **目标**: 提升系统韧性，并为多用户使用和成本控制打下基础。
  * **任务**:

    * [ ] **韧性**: 实现下游调用的自动重试和熔断器机制。
    * [ ] **计量**: 实现`request_log`​表的完整记录功能。
    * [ ] **可观测性**: 集成分布式追踪。
    * [ ] **安全**: 启动用户登录 (FR-03) 和系统级密钥管理 (FR-04) 的开发。
    * [ ] **扩展性验证**: 在`ulg-adapter-core`​中增加对另一种模型（如Claude）的适配器。
* **Phase 3: 运营与高级功能 (长期)**

  * **目标**: 打造完整的运营能力，提供企业级功能。
  * **任务**:

    * [ ] **成本控制**: 实现基于系统Key的速率限制和用量配额。
    * [ ] **高级容错**: 实现备用模型/回退策略。
    * [ ] **运营**: 搭建完整的监控仪表盘 (Grafana) 和日志查询系统 (Kibana/Loki)。
    * [ ] **发布与文档**: 规范化`ulg-adapter-core`​模块的发布流程，并编写详细的部署和使用文档。

我将根据您的新要求，对整个PRD文档进行重构和优化，重点突出多模块架构带来的优势。

---

### **PRD: 大模型统一接入网关 (Unified LLM Gateway) 项目**

|文档版本|V1.1|
| :---------| :--------------------------------|
|**创建日期**|2023-10-27|
|**最后更新**|2023-10-28 (根据多模块架构重构)|
|**创建人**|[您的名字]|
|**项目代号**|ULG (Unified LLM Gateway)|
|**状态**|修订稿|

---

|文档版本|V2.0 (正式版)|
| :---------| :--------------------------|
|**创建日期**|2025-7-10|
|**创建人**|[您的名字]|
|**项目代号**|ULG (Unified LLM Gateway)|
|**状态**|已定稿|

---

### **1. 项目概述 (Project Overview)**

#### **1.1. 项目背景 (Background)**

随着大语言模型（LLM）技术的飞速发展，市场上涌现出 OpenAI (ChatGPT), Google (Gemini), Anthropic (Claude) 等众多优秀的服务提供商。然而，这些提供商的API在请求/响应格式、认证方式、参数定义上各不相同，给开发者带来了巨大的挑战：

* **高昂的切换成本**：当业务需要从一个模型切换到另一个模型时，往往需要重写大量的API调用代码。
* **复杂的适配逻辑**：在同一个应用中集成多个模型时，开发者需要维护多套请求和响应处理逻辑，增加了代码的复杂性和维护难度。
* **管理不便**：不同服务商的密钥（API Key）、Base URL等配置信息分散，难以统一管理和轮换。

本项目旨在解决以上痛点，通过构建一个“大模型统一接入网关”，对外提供标准化的、与OpenAI API格式完全兼容的接口。它作为中间层，将用户的请求动态地转换为目标服务商所需的格式，并将返回结果再转换成OpenAI的格式，从而实现对上层应用的透明化接入。

#### **1.2. 目标用户 (Target Audience)**

* **应用开发者**：希望快速集成、灵活切换不同LLM服务的开发者。
* **中小型企业/团队**：需要统一管理LLM资源、降低开发和维护成本的团队。
* **AI服务集成商**：需要为客户提供稳定、多模型支持的解决方案的服务商。

#### **1.3. 项目目标 (Project Goals)**

1. **核心目标**: 实现一个稳定、高效的API网关，将OpenAI格式的API请求无缝适配到多种下游LLM服务提供商。
2. **产品目标**: 提供一个简洁易用的管理后台，方便用户对LLM服务提供商进行全生命周期管理（增删改查）。
3. **架构目标**: 采用**Maven多模块架构**，将核心的适配转换逻辑 (`adapter`​层) 与Web服务层 (`web`​层) 彻底分离，使核心适配逻辑可作为独立的Java库被其他项目复用。
4. **长期愿景**: 成为一个功能完善的LLM Ops平台，提供模型路由、负载均衡、缓存、日志、监控告警等增值服务。

---

### **2. 功能需求 (Functional Requirements)**

#### **2.1. 核心模块：API适配网关 (FR-01)**

* **FR-01.01: OpenAI兼容接口**

  * **描述**: 系统必须提供与OpenAI官方API `/v1/chat/completions`​ 完全兼容的端点。
  * **验收标准**:

    1. 支持 `POST /v1/chat/completions`​ 请求。
    2. 支持 `stream`​ 模式（流式响应）和非流式响应。
    3. 请求头（Header）中通过 `Authorization: Bearer <token>`​ 传递认证信息。
    4. 请求体（Body）支持OpenAI `ChatCompletion`​ 的标准JSON结构，包括 `model`​, `messages`​, `temperature`​, `max_tokens`​ 等核心字段。
* **FR-01.02: 请求格式转换**

  * **描述**: 网关接收到OpenAI格式的请求后，能根据路由规则将其转换为指定下游服务商（如Gemini）的API请求格式。
  * **验收标准**:

    1. 能正确解析OpenAI请求中的 `model`​ 字段，并映射到后台配置的LLM服务提供商。
    2. 能将 `messages`​ 数组（包含 `role`​ 和 `content`​）转换为目标服务商所需的格式。
    3. 能将 `temperature`​, `max_tokens`​ 等参数适配为目标服务商的对应字段。
    4. 能正确构建发往下游服务的HTTP请求，包括URL、Header和Body。
* **FR-01.03: 响应格式转换**

  * **描述**: 网关接收到下游服务商的响应后，能将其转换为标准的OpenAI API响应格式。
  * **验收标准**:

    1. **非流式**: 能将下游响应体解析并封装成OpenAI的 `ChatCompletion`​ 对象格式。
    2. **流式**: 能将下游的流式响应（Server-Sent Events）实时转换为OpenAI的流式`ChatCompletionChunk`​格式。
    3. 能正确处理和转换下游服务商的错误信息，并以OpenAI的错误格式返回。

#### **2.2. 后台管理模块：服务商管理 (FR-02)**

* **FR-02.01: 服务商列表查询**

  * **描述**: 用户可以在管理界面查看所有已录入的LLM服务提供商列表。
  * **验收标准**:

    1. 列表以表格形式展示，包含字段：提供商名称、提供商类型、模型名称、Base URL、创建时间。
    2. 支持分页和按提供商/模型名称进行模糊搜索。
* **FR-02.02: 新增服务商**

  * **描述**: 用户可以通过表单录入一个新的LLM服务提供商。
  * **验收标准**:

    1. 表单包含字段：提供商名称、提供商类型（下拉框）、模型名称（必须唯一）、API密钥（密码框）、Base URL、备注。
    2. 提交后对表单数据进行校验，API密钥在存储前必须加密。
* **FR-02.03: 编辑服务商**

  * **描述**: 用户可以修改已录入的服务商信息。
  * **验收标准**:

    1. 在列表页提供“编辑”按钮，点击后弹出表单并回填已有数据。
    2. 模型名称（Model Name）作为唯一标识，不可修改。
* **FR-02.04: 删除服务商**

  * **描述**: 用户可以删除不再使用的服务商配置。
  * **验收标准**:

    1. 在列表页提供“删除”按钮。
    2. 点击后有二次确认弹窗，防止误操作。

#### **2.3. 待办功能模块 (Future Scope)**

* **FR-03.01: 用户登录与权限管理**: 引入用户系统，只有登录用户才能访问管理后台。
* **FR-04.01: 系统级密钥管理**: 为本网关系统生成和管理API密钥，实现更精细的访问控制和用量统计。

---

### **3. 非功能性需求 (Non-Functional Requirements)**

|类别|需求描述|
| :-----| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|**性能 (Performance)**|- API网关核心转换逻辑的额外延迟应控制在50ms以内。<br>- 系统应能支持至少100 QPS的并发请求。|
|**可扩展性 (Scalability)**|- **模块化扩展**: 基于Maven多模块架构，增加新的服务商类型时，只需新增一个适配器实现模块，对主应用无侵入。<br>- **组件复用**: `ulg-adapter`​模块可被打包成库，供其他Java项目直接引入，实现大模型适配能力的复用。<br>- **数据库兼容**: 使用JPA，确保系统能无缝切换SQLite（开发/测试）和MySQL/PostgreSQL（生产）。|
|**安全性 (Security)**|- 所有存储在数据库中的敏感信息（如API密钥）必须进行强加密处理。<br>- 前后端通信强制使用HTTPS。<br>- 遵循安全编码规范，防止常见的Web攻击（SQL注入、XSS、CSRF等）。|
|**可用性 (Availability)**|- 系统应保证99.9%的可用性。<br>- 提供优雅的降级策略，当下游服务商不可用时，能返回明确的错误信息。|
|**可维护性 (Maintainability)**|- 提供完善的日志记录，包括请求入参、转换过程、下游响应和最终响应，便于问题排查。<br>- 代码结构清晰，严格遵循多模块划分，职责单一。|

---

### **4. 技术栈与架构 (Technology &amp; Architecture)**

#### **4.1. 技术栈**

* **后端**: Spring Boot 3.x, Spring Web, Spring Data JPA, Spring Security (后续引入), Lombok。
* **构建与依赖管理**: **Maven (多模块项目)**
* **数据库**: JPA + Hibernate。开发环境使用 SQLite 或 H2，生产环境推荐 MySQL 8.0+ 或 PostgreSQL。
* **前端**: Vue 3, Vite, Vue Router, Pinia, Axios, Element Plus / Naive UI。

#### **4.2. Maven模块化架构设计**

项目将采用父-子模块结构，以实现关注点分离和代码复用。

* ​`ulg-parent`​: 项目的根模块，使用`<packaging>pom</packaging>`​。负责管理所有子模块的公共依赖版本和插件配置。

  * ​`ulg-common`​: 通用工具模块。包含整个项目共享的实体类(Entity)、DTO、常量、自定义异常和工具类。
  * ​`ulg-adapter`​: 适配器父模块，聚合所有具体的适配器实现。

    * ​`ulg-adapter-core`​: **核心适配器接口模块**。定义适配器模式的标准接口（如`LLMAdapter`​）、统一的请求/响应模型。此模块轻量且无第三方依赖。
    * ​`ulg-adapter-gemini`​: **Gemini适配器实现模块**。依赖`ulg-adapter-core`​，并实现其接口，负责OpenAI与Gemini格式之间的双向转换逻辑。
    * ​`...`​: (未来可添加 `ulg-adapter-claude`​, `ulg-adapter-ernie`​ 等模块)
  * ​`ulg-web`​: **Web网关应用模块**。这是一个可执行的Spring Boot应用。它依赖`ulg-common`​和所有`ulg-adapter-*`​实现模块。包含Controllers、Services、Repositories等，负责处理HTTP请求、调用适配器并与数据库交互。

#### **4.3. 简要架构图**

```mermaid
graph TD
    subgraph "外部交互"
        direction LR
        A["用户/客户端应用"]
        G["管理员"]
        D1["Gemini API"]
        D2["其他LLM API"]
    end

    subgraph "ULG 系统 (Maven 多模块)"
        direction TB
  
        subgraph "ulg-web (可执行网关)"
            direction LR
            B["Controller (REST API)"];
            F["JPA / DB Access"];
            B -- 调用 --> S["Service Layer"];
        end
  
        subgraph "ulg-adapter (可复用库)"
            direction LR
            S -- 使用 --> C["Adapter Facade / Dispatcher"];
            subgraph "ulg-adapter-core"
                CI["Adapter Interface"];
            end
            subgraph "ulg-adapter-gemini"
                C1["Gemini Adapter Impl"];
            end
            subgraph "ulg-adapter-other"
                C2["Other Adapter Impl"];
            end
            C --> CI;
            C1 -- 实现 --> CI;
            C2 -- 实现 --> CI;
        end
  
        E["后台管理前端 (Vue)"]

    end

    A -- "1. OpenAI格式请求" --> B;
    B -- "6. OpenAI格式响应" --> A;
  
    S -- "读/写配置" --> F;
    C1 -- "3/4. 请求/响应" --> D1;
    C2 -- "3.5/4.5. 请求/响应" --> D2;
  
    G -- "操作" --> E;
    E -- "API调用" --> B;

    style D1 fill:#f9f,stroke:#333,stroke-width:2px
    style D2 fill:#f9f,stroke:#333,stroke-width:2px
```

---

### **5. 数据模型设计 (Data Model)**

**表名**: `llm_provider`​

|字段名 (Column)|类型 (Type)|约束 (Constraints)|描述 (Description)|
| :----------------| :------------| :-------------------| :--------------------------------------|
|​`id`​|​`BIGINT`​|主键, 自增|唯一标识符|
|​`provider_name`​|​`VARCHAR(100)`​|NOT NULL|提供商的自定义名称，用于UI展示|
|​`provider_type`​|​`VARCHAR(50)`​|NOT NULL|提供商类型 (e.g., 'GEMINI', 'OPENAI')|
|​`model_name`​|​`VARCHAR(100)`​|NOT NULL, UNIQUE|模型的唯一标识，用于API请求路由|
|​`api_key`​|​`VARCHAR(512)`​|NOT NULL|加密后存储的API密钥|
|​`base_url`​|​`VARCHAR(255)`​|NOT NULL|API的Base URL|
|​`notes`​|​`TEXT`​|NULL|备注信息|
|​`created_at`​|​`DATETIME`​|NOT NULL|创建时间|
|​`updated_at`​|​`DATETIME`​|NOT NULL|更新时间|

---

### **6. API 接口设计 (API Design)**

#### **6.1. 网关核心API**

* **Endpoint**: `POST /v1/chat/completions`​
* **描述**: 接收符合OpenAI规范的聊天请求，并代理到下游服务商。
* **请求/响应**: 完全遵循OpenAI ChatCompletion API规范。

#### **6.2. 后台管理API**

* **基路径**: `/api/admin/providers`​

|方法|URL|描述|
| :-----| :-----| :---------------------------------|
|​`POST`​|​`/`​|新增一个服务商|
|​`GET`​|​`/`​|获取服务商列表（支持分页和搜索）|
|​`GET`​|​`/{id}`​|获取指定ID的服务商详情|
|​`PUT`​|​`/{id}`​|更新指定ID的服务商信息|
|​`DELETE`​|​`/{id}`​|删除指定ID的服务商|

---

### **7. 项目里程碑 (Roadmap)**

* **Phase 1: MVP (Minimum Viable Product) - 预计4周**

  * **目标**: 搭建多模块项目骨架，实现核心转换功能。
  * **任务**:

    * [ ] **后端**: 搭建Maven多模块项目结构 (`parent`​, `common`​, `adapter-core`​, `adapter-gemini`​, `web`​)。
    * [ ] **后端**: 在 `ulg-common`​ 模块中定义`LlmProvider`​实体类和相关DTO。
    * [ ] **后端**: 在 `ulg-adapter-core`​ 模块中定义核心的适配器接口和模型。
    * [ ] **后端**: 在 `ulg-adapter-gemini`​ 模块中，实现OpenAI-to-Gemini的具体转换逻辑（流式与非流式）。
    * [ ] **后端**: 在 `ulg-web`​ 模块中，完成服务商管理的CRUD接口和`/v1/chat/completions`​网关接口。
    * [ ] **前端**: 搭建Vue项目，完成服务商管理的增、删、改、查界面。
    * [ ] **集成**: 完成前后端联调和端到端测试。
* **Phase 2: 功能增强 &amp; 组件化发布 - 预计2周**

  * **目标**: 验证架构的可扩展性并准备`adapter`​库的发布。
  * **任务**:

    * [ ] 增加对至少一种新模型（如Claude或国产模型）的适配器作为扩展性验证。
    * [ ] 配置`adapter`​模块的打包发布流程，使其可以被部署到私有Maven仓库。
    * [ ] 完善日志系统和全局异常处理。
    * [ ] 优化前端交互体验和表单校验。
* **Phase 3: 安全与成熟度提升 - (长期)**

  * **目标**: 为生产环境使用做准备。
  * **任务**:

    * [ ] 在 `ulg-web`​ 中引入Spring Security，实现基于JWT或Session的用户登录和后台接口保护。
    * [ ] 实现系统级密钥管理模块，应用调用网关需使用系统颁发的Key。
    * [ ] 增加简单的用量统计和仪表盘功能。
    * [ ] 编写详细的部署文档和`adapter`​库的使用说明文档。

---

### 1. 项目概述 (Project Overview)

#### 1.1. 项目背景 (Background)

随着大语言模型（LLM）技术的飞速发展，市场上涌现出 OpenAI (ChatGPT), Google (Gemini), Anthropic (Claude), Baidu (ERNIE Bot) 等众多优秀的服务提供商。然而，这些提供商的API在请求/响应格式、认证方式、参数定义上各不相同，给开发者带来了巨大的挑战：

* ‍
* ‍
* ‍

#### 1.2. 目标用户 (Target Audience)

（内容同V1.0，此处省略）

#### 1.3. 项目目标 (Project Goals)

1. **核心目标**: 实现一个稳定、高效的API网关，将OpenAI格式的API请求无缝适配到多种下游LLM服务提供商。
2. **产品目标**: 提供一个简洁易用的管理后台，方便用户对LLM服务提供商进行全生命周期管理（增删改查）。
3. **架构目标 (已更新)** :

    * **模块化与解耦**: 采用**Maven多模块架构**，将核心的适配转换逻辑 (`adapter`​层) 与Web服务层 (`web`​层) 彻底分离。
    * **组件可复用**: 将`adapter`​层构建为可独立发布的Java库。任何需要集成多LLM的应用，都可以直接引入该库，而无需部署整个网关，极大地提升了核心代码的复用价值。
4. **长期愿景**: 成为一个功能完善的LLM Ops平台，提供模型路由、负载均衡、缓存、日志、监控告警等增值服务。

---

### 2. 功能需求 (Functional Requirements)

（功能需求的核心内容不变，因为它们描述的是“做什么”，而不是“怎么做”。架构的改变影响的是实现方式。）

#### 2.1. 核心模块：API适配网关 (FR-01)

* **FR-01.01: OpenAI兼容接口**
* **FR-01.02: 请求格式转换**
* **FR-01.03: 响应格式转换**

#### 2.2. 后台管理模块：服务商管理 (FR-02)

* **FR-02.01: 服务商列表查询**
* **FR-02.02: 新增服务商**
* **FR-02.03: 编辑服务商**
* **FR-02.04: 删除服务商**

#### 2.3. 待办功能模块 (Future Scope)

* **FR-03.01: 用户登录与权限管理**
* **FR-04.01: 系统级密钥管理**

---

### 3. 非功能性需求 (Non-Functional Requirements)

|类别|需求描述 (已更新)|
| :-----| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|**性能 (Performance)**|- API网关核心转换逻辑的额外延迟应控制在50ms以内。<br>- 系统应能支持至少100 QPS的并发请求（初期目标）。|
|**可扩展性 (Scalability)**|- **模块化扩展**: 基于Maven多模块架构，核心适配逻辑位于独立模块。增加新的服务商类型（如Claude）时，可以创建新的`ulg-adapter-claude`​子模块，对主应用无侵入，实现了真正的插件化。<br>- **组件复用**: `ulg-adapter-core`​及其实现模块可被打包成库，供其他Java/Kotlin项目直接引入，实现大模型适配能力的复用。<br>- **数据库兼容**: 使用JPA，确保系统能无缝切换SQLite（开发/测试）和MySQL/PostgreSQL（生产）。|
|**安全性 (Security)**|- 所有存储在数据库中的敏感信息（如API密钥）必须进行强加密处理。<br>- 前后端通信使用HTTPS。<br>- 防止常见的Web攻击，如SQL注入、XSS等。|
|**可用性 (Availability)**|- 系统应保证99.9%的可用性。<br>- 提供优雅的降级策略，当下游服务商不可用时，能返回明确的错误信息。|
|**可维护性 (Maintainability)**|- 提供完善的日志记录，便于问题排查。<br>- **代码结构清晰**：严格遵循多模块划分，职责单一（web层负责http，adapter层负责转换）。|

---

### 4. 技术栈与架构 (Technology & Architecture) - **核心更新**

#### 4.1. 技术栈

* **后端**: Spring Boot 3.x, Spring Web, Spring Data JPA, Spring Security (后续引入), Lombok。
* **构建与依赖管理**: **Maven (多模块项目)**
* **数据库**: JPA + Hibernate。开发环境使用 H2/SQLite，生产环境推荐 MySQL 8.0+ 或 PostgreSQL。
* **前端**: Vue 3, Vite, Vue Router, Pinia, Axios, Element Plus / Naive UI。

#### 4.2. Maven模块化架构设计

项目将采用父-子模块结构，以实现关注点分离和代码复用。

* ​`ulg-parent`​: 项目的根模块，使用`<packaging>pom</packaging>`​。负责管理所有子模块的公共依赖版本、插件配置和属性。

  * ​`ulg-common`​: 通用工具模块。包含整个项目共享的实体类(Entity)、DTO、常量、自定义异常和工具类。
  * ​`ulg-adapter`​: 适配器父模块，聚合所有具体的适配器实现。

    * ​`ulg-adapter-core`​: **核心适配器接口模块**。定义适配器模式的标准接口（如`LLMAdapter`​）、统一的请求/响应模型。**此模块不包含任何第三方依赖，非常轻量**。
    * ​`ulg-adapter-gemini`​: **Gemini适配器实现模块**。依赖`ulg-adapter-core`​，并实现其接口。负责OpenAI与Gemini格式之间的双向转换逻辑。
    * ​`ulg-adapter-openai`​: (可选) 未来可实现一个透传或到其他OpenAI兼容服务的适配器。
    * ... (未来可添加 `ulg-adapter-claude`​ 等)
  * ​`ulg-web`​: **Web网关应用模块**。这是一个可执行的Spring Boot应用。

    * 依赖 `ulg-common`​ 和 `ulg-adapter`​。
    * 包含Controllers、Services、Repositories、Security配置等。
    * 负责处理HTTP请求，调用`adapter`​层完成逻辑转换，并与数据库交互。
    * 包含前端静态资源。

#### 4.3. 简要架构图 (已更新)

```mermaid
graph TD
    subgraph "外部交互"
        direction LR
        A[用户/客户端应用]
        G[管理员]
        D1[Gemini API]
        D2[其他LLM API]
    end

    subgraph "ULG 系统 (Maven 多模块)"
        direction TB
  
        subgraph "ulg-web (可执行网关)"
            direction LR
            B["Controller (REST API)"];
            F["JPA / DB Access"];
            B -- 调用 --> S["Service Layer"];
        end
  
        subgraph "ulg-adapter (可复用库)"
            direction LR
            S -- 使用 --> C["Adapter Facade / Dispatcher"];
            subgraph "ulg-adapter-core"
                CI[Adapter Interface];
            end
            subgraph "ulg-adapter-gemini"
                C1[Gemini Adapter Impl];
            end
            subgraph "ulg-adapter-other"
                C2[Other Adapter Impl];
            end
            C --> CI;
            C1 -- 实现 --> CI;
            C2 -- 实现 --> CI;
        end
  
        E["后台管理前端 (Vue)"]

    end

    A -- "1. OpenAI格式请求" --> B;
    B -- "6. OpenAI格式响应" --> A;
  
    S -- 读写配置 --> F;
    C1 -- "3/4. 请求/响应" --> D1;
    C2 -- "3/4. 请求/响应" --> D2;
  
    G -- 操作 --> E;
    E -- API调用 --> B;

    style D1 fill:#f9f,stroke:#333,stroke-width:2px
    style D2 fill:#f9f,stroke:#333,stroke-width:2px
```

---

### 5. 数据模型设计 (Data Model)

**表名**: `llm_provider`​  
(表结构设计保持不变)

|字段名 (Column)|类型 (Type)|约束 (Constraints)|描述 (Description)|
| :----------------| :------------| :-------------------| :--------------------------------------|
|​`id`​|​`BIGINT`​|主键, 自增|唯一标识符|
|​`provider_name`​|​`VARCHAR(100)`​|NOT NULL|提供商的自定义名称，用于UI展示|
|​`provider_type`​|​`VARCHAR(50)`​|NOT NULL|提供商类型 (e.g., 'GEMINI', 'OPENAI')|
|​`model_name`​|​`VARCHAR(100)`​|NOT NULL, UNIQUE|模型的唯一标识，用于API请求路由|
|​`api_key`​|​`VARCHAR(512)`​|NOT NULL|加密后的API密钥|
|​`base_url`​|​`VARCHAR(255)`​|NOT NULL|API的Base URL|
|​`notes`​|​`TEXT`​|NULL|备注信息|
|​`created_at`​|​`DATETIME`​|NOT NULL|创建时间|
|​`updated_at`​|​`DATETIME`​|NOT NULL|更新时间|

---

### 6. API 接口设计 (API Design)

(对外暴露的API接口保持不变)

#### 6.1. 网关核心API

* **Endpoint**: `POST /v1/chat/completions`​

#### 6.2. 后台管理API

* **基路径**: `/api/admin/providers`​

  * ​`POST /`​
  * ​`GET /`​
  * ​`GET /{id}`​
  * ​`PUT /{id}`​
  * ​`DELETE /{id}`​

---

### 7. 项目里程碑 (Roadmap) - **已更新**

* **Phase 1: MVP (Minimum Viable Product) - 预计4周**

  * **目标**: 搭建多模块项目骨架，实现核心转换功能。
  * **任务**:

    * [ ] **后端**: 搭建Maven多模块项目结构 (`parent`​, `common`​, `adapter-core`​, `adapter-gemini`​, `web`​)。
    * [ ] **后端**: 在 `ulg-common`​ 模块中定义`LlmProvider`​实体类和相关DTO。
    * [ ] **后端**: 在 `ulg-adapter-core`​ 模块中定义核心的适配器接口和模型。
    * [ ] **后端**: 在 `ulg-adapter-gemini`​ 模块中，实现OpenAI-to-Gemini的具体转换逻辑。
    * [ ] **后端**: 在 `ulg-web`​ 模块中，完成服务商管理的CRUD接口和`/v1/chat/completions`​网关接口。
    * [ ] **前端**: 搭建Vue项目，完成服务商管理的增、删、改、查界面。
    * [ ] **集成**: 完成前后端联调和端到端测试。
* **Phase 2: 功能增强 &amp; 组件化发布 - 预计2周**

  * **目标**: 验证架构的可扩展性并准备`adapter`​库的发布。
  * **任务**:

    * [ ] 增加对另一种模型（如Claude）的适配器`ulg-adapter-claude`​作为扩展性验证。
    * [ ] **配置**​**​`adapter`​**​**模块的打包发布流程**，使其可以被部署到私有Maven仓库。
    * [ ] 完善日志系统和全局异常处理。
    * [ ] 优化前端交互体验。
* **Phase 3: 安全与成熟度提升 - (长期)**

  * **目标**: 为生产环境使用做准备。
  * **任务**:

    * [ ] 在 `ulg-web`​ 中引入Spring Security，实现用户登录和后台接口保护。
    * [ ] 实现系统级密钥管理模块。
    * [ ] 增加用量统计和仪表盘。
    * [ ] 编写详细的部署文档和`adapter`​库的使用文档。
