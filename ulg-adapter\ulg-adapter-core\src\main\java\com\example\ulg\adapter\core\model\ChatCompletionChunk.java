package com.example.ulg.adapter.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 标准的 OpenAI Chat Completion 流式响应块模型。
 * 对应于 `POST /v1/chat/completions` 的流式响应中的单个 `data:` 对象。
 */
@Data
@Builder
public class ChatCompletionChunk {

    private String id;
    private String object;
    private long created;
    private String model;
    private List<ChunkChoice> choices;

    @Data
    @Builder
    public static class ChunkChoice {
        private int index;
        private Delta delta;
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    @Data
    @Builder
    public static class Delta {
        private String role;
        private String content;
    }
}
