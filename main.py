
import json
from flask import Flask, request, jsonify, Response
import aiohttp
import asyncio
import base64
import mimetypes
import uuid
import time
import re

app = Flask(__name__)

# 定义常量
SAFETY_SETTING_LIST = [
    "HARM_CATEGORY_HARASSMENT",
    "HARM_CATEGORY_HATE_SPEECH",
    "HARM_CATEGORY_SEXUALLY_EXPLICIT",
    "HARM_CATEGORY_DANGEROUS_CONTENT",
]

# 模拟模型设置
class ModelSetting:
    def __init__(self):
        self.gemini_safety_settings = {category: "BLOCK_NONE" for category in SAFETY_SETTING_LIST}
        self.thinking_adapter_enabled = True
        self.thinking_adapter_budget_tokens_percentage = 0.5
        self.models_supported_thinking_budget = ["gemini-pro"] # 示例模型
        self.gemini_model_support_imagine = ["gemini-pro-vision"] # 示例模型

    def get_gemini_safety_setting(self, category):
        return self.gemini_safety_settings.get(category, "BLOCK_NONE")

    def is_gemini_model_support_imagine(self, model_name):
        return model_name in self.gemini_model_support_imagine

model_setting = ModelSetting()

# 模拟服务函数
async def get_file_base64_from_url(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.read()
                mime_type, _ = mimetypes.guess_type(url)
                if mime_type is None:
                    mime_type = 'application/octet-stream'
                return {"mime_type": mime_type, "base64_data": base64.b64encode(data).decode('utf-8')}
    raise Exception(f"Failed to fetch file from url: {url}")

def decode_base64_file_data(encoded_data):
    # "data:image/png;base64,iVBORw0KGgo..."
    match = re.match(r"data:(?P<mime_type>[\w/]+);base64,(?P<data>.+)", encoded_data)
    if match:
        mime_type = match.group('mime_type')
        data = match.group('data')
        return mime_type, data
    raise ValueError("Invalid base64 data URI")


@app.route('/v1/chat/completions', methods=['POST'])
async def chat_completions():
    """
    将OpenAI格式的请求转换为Gemini格式，并模拟调用Gemini API。
    """
    openai_request_data = request.json
    api_key = request.headers.get("Authorization", "").replace("Bearer ", "")
    
    if not api_key:
        return jsonify({"error": "Authorization header is missing"}), 401

    try:
        gemini_request, err = await convert_openai_to_gemini(openai_request_data)
        if err:
            return jsonify({"error": str(err)}), 400
        
        # 假设我们在这里调用Gemini API
        # gemini_response = await call_gemini_api(gemini_request, api_key)

        # 为了演示，我们直接将Gemini请求返回
        # 在实际应用中，您应该调用Gemini API并转换其响应
        # return jsonify(gemini_request)

        # 模拟一个Gemini响应
        gemini_response = {
            "candidates": [
                {
                    "content": {
                        "parts": [
                            {"text": "This is a simulated response from Gemini."}
                        ],
                        "role": "model"
                    },
                    "finishReason": "STOP",
                    "index": 0,
                    "safetyRatings": []
                }
            ],
            "usageMetadata": {
                "promptTokenCount": 10,
                "candidatesTokenCount": 8,
                "totalTokenCount": 18
            }
        }

        # 将Gemini响应转换为OpenAI格式
        openai_response = convert_gemini_to_openai(gemini_response, openai_request_data.get('model'))
        
        if openai_request_data.get('stream', False):
            def generate():
                for choice in openai_response.get("choices", []):
                    chunk = {
                        "id": f"chatcmpl-{uuid.uuid4()}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": openai_response.get("model"),
                        "choices": [{
                            "index": choice["index"],
                            "delta": {"role": "assistant", "content": choice["message"]["content"]},
                            "finish_reason": choice.get("finish_reason")
                        }]
                    }
                    yield f"data: {json.dumps(chunk)}

"
                yield "data: [DONE]

"
            return Response(generate(), mimetype='text/event-stream')
        else:
            return jsonify(openai_response)


    except Exception as e:
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500


async def convert_openai_to_gemini(openai_req):
    """
    将OpenAI请求格式转换为Gemini请求格式。
    """
    gemini_req = {
        "contents": [],
        "generationConfig": {
            "temperature": openai_req.get("temperature", 0.7),
            "topP": openai_req.get("top_p", 1.0),
            "maxOutputTokens": openai_req.get("max_tokens", 2048),
            "seed": openai_req.get("seed", 0),
        },
        "safetySettings": [
            {"category": category, "threshold": model_setting.get_gemini_safety_setting(category)}
            for category in SAFETY_SETTING_LIST
        ]
    }

    system_content = []
    for message in openai_req.get("messages", []):
        if message["role"] == "system":
            system_content.append(message["content"])
            continue
        
        content = {"role": message["role"] if message["role"] != "assistant" else "model", "parts": []}
        
        if isinstance(message["content"], str):
             content["parts"].append({"text": message["content"]})
        elif isinstance(message["content"], list):
            for part in message["content"]:
                if part["type"] == "text":
                    content["parts"].append({"text": part["text"]})
                elif part["type"] == "image_url":
                    image_url = part["image_url"]["url"]
                    if image_url.startswith("http"):
                        file_data = await get_file_base64_from_url(image_url)
                        content["parts"].append({"inline_data": {"mime_type": file_data["mime_type"], "data": file_data["base64_data"]}})
                    else:
                        mime_type, data = decode_base64_file_data(image_url)
                        content["parts"].append({"inline_data": {"mime_type": mime_type, "data": data}})

        gemini_req["contents"].append(content)

    if system_content:
        gemini_req["systemInstruction"] = {"parts": [{"text": "
".join(system_content)}]}
    
    return gemini_req, None

def convert_gemini_to_openai(gemini_res, model_name):
    """
    将Gemini响应格式转换为OpenAI响应格式。
    """
    openai_res = {
        "id": f"chatcmpl-{uuid.uuid4()}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model_name,
        "choices": [],
        "usage": {
            "prompt_tokens": gemini_res.get("usageMetadata", {}).get("promptTokenCount", 0),
            "completion_tokens": gemini_res.get("usageMetadata", {}).get("candidatesTokenCount", 0),
            "total_tokens": gemini_res.get("usageMetadata", {}).get("totalTokenCount", 0),
        }
    }

    for i, candidate in enumerate(gemini_res.get("candidates", [])):
        text_parts = [part["text"] for part in candidate.get("content", {}).get("parts", []) if "text" in part]
        choice = {
            "index": i,
            "message": {
                "role": "assistant",
                "content": "
".join(text_parts),
            },
            "finish_reason": candidate.get("finishReason", "stop").lower(),
        }
        openai_res["choices"].append(choice)
        
    return openai_res


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
