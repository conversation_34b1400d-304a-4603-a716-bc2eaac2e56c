<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>ulg-adapter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ulg-adapter-gemini</artifactId>

    <name>ulg-adapter-gemini</name>
    <description>Gemini适配器实现模块</description>

    <dependencies>
        <!-- 依赖核心适配器接口 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>ulg-adapter-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 内部依赖：核心适配器模块 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>ulg-adapter-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Context for @Component -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Project Reactor for reactive types like Flux -->
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
    </dependencies>

</project>
