package com.example.ulg.adapter.gemini.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GeminiChatRequest {

    private List<Content> contents;

    @JsonProperty("generation_config")
    private GenerationConfig generationConfig;

    @JsonProperty("system_instruction")
    private SystemInstruction systemInstruction;

    @JsonProperty("safety_settings")
    private List<SafetySetting> safetySettings;

    @Data
    @Builder
    public static class Content {
        private String role; // "user" or "model"
        private List<Part> parts;
    }

    @Data
    @Builder
    public static class Part {
        private String text;
        @JsonProperty("inline_data")
        private InlineData inlineData;
    }

    @Data
    @Builder
    public static class InlineData {
        @JsonProperty("mime_type")
        private String mimeType;
        private String data;
    }

    @Data
    @Builder
    public static class GenerationConfig {
        private Double temperature;
        @JsonProperty("top_p")
        private Double topP;
        @JsonProperty("max_output_tokens")
        private Integer maxOutputTokens;
        @JsonProperty("stop_sequences")
        private List<String> stopSequences;
        private Integer seed;
    }
    
    @Data
    @Builder
    public static class SystemInstruction {
        private List<Part> parts;
    }

    @Data
    @Builder
    public static class SafetySetting {
        private String category;
        private String threshold;
    }
}
