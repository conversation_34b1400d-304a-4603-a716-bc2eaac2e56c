package com.example.ulg.adapter.gemini;

import com.example.ulg.adapter.core.LlmAdapter;
import com.example.ulg.adapter.core.model.ChatCompletionChunk;
import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import com.example.ulg.adapter.gemini.model.GeminiChatRequest;
import com.example.ulg.adapter.gemini.model.GeminiChatResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class GeminiAdapter implements LlmAdapter {

    private final ObjectMapper objectMapper;
    private static final String PROVIDER_TYPE = "GEMINI";

    private static final List<String> SAFETY_CATEGORIES = List.of(
            "HARM_CATEGORY_HARASSMENT",
            "HARM_CATEGORY_HATE_SPEECH",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "HARM_CATEGORY_DANGEROUS_CONTENT"
    );

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    @Override
    public Object convertRequest(ChatCompletionRequest request) {
        // 1. 构建 GenerationConfig
        GeminiChatRequest.GenerationConfig generationConfig = GeminiChatRequest.GenerationConfig.builder()
                .temperature(request.getTemperature())
                .topP(request.getTopP())
                .maxOutputTokens(request.getMaxTokens())
                .stopSequences(request.getStop())
                .seed(request.getSeed())
                .build();

        // 2. 构建 SafetySettings
        List<GeminiChatRequest.SafetySetting> safetySettings = SAFETY_CATEGORIES.stream()
                .map(category -> GeminiChatRequest.SafetySetting.builder()
                        .category(category)
                        .threshold("BLOCK_NONE")
                        .build())
                .collect(Collectors.toList());

        // 3. 处理消息
        List<ChatCompletionRequest.Message> openAiMessages = request.getMessages();
        GeminiChatRequest.SystemInstruction systemInstruction = null;
        List<GeminiChatRequest.Content> contents = new ArrayList<>();

        // 提取 System Prompt
        List<String> systemPrompts = openAiMessages.stream()
                .filter(m -> "system".equalsIgnoreCase(m.getRole()))
                .map(ChatCompletionRequest.Message::getContentAsString)
                .collect(Collectors.toList());

        if (!systemPrompts.isEmpty()) {
            GeminiChatRequest.Part systemPart = GeminiChatRequest.Part.builder()
                    .text(String.join("\n", systemPrompts))
                    .build();
            systemInstruction = GeminiChatRequest.SystemInstruction.builder()
                    .parts(Collections.singletonList(systemPart))
                    .build();
        }

        // 转换 User 和 Assistant 消息
        for (ChatCompletionRequest.Message message : openAiMessages) {
            if ("system".equalsIgnoreCase(message.getRole())) {
                continue;
            }
            contents.add(mapToGeminiContent(message));
        }

        return GeminiChatRequest.builder()
                .contents(contents)
                .systemInstruction(systemInstruction)
                .generationConfig(generationConfig)
                .safetySettings(safetySettings)
                .build();
    }

    private GeminiChatRequest.Content mapToGeminiContent(ChatCompletionRequest.Message message) {
        String role = "user".equalsIgnoreCase(message.getRole()) ? "user" : "model";
        List<GeminiChatRequest.Part> parts = new ArrayList<>();

        Object content = message.getContent();
        if (content instanceof String) {
            parts.add(GeminiChatRequest.Part.builder().text((String) content).build());
        } else if (content instanceof List) {
            // 使用 Jackson 进行类型转换，处理多模态内容
            List<ChatCompletionRequest.ContentPart> contentParts = objectMapper.convertValue(content, new TypeReference<List<ChatCompletionRequest.ContentPart>>() {});
            for (ChatCompletionRequest.ContentPart part : contentParts) {
                if ("text".equals(part.getType())) {
                    parts.add(GeminiChatRequest.Part.builder().text(part.getText()).build());
                } else if ("image_url".equals(part.getType())) {
                    GeminiChatRequest.InlineData inlineData = convertToInlineData(part.getImageUrl().getUrl());
                    parts.add(GeminiChatRequest.Part.builder().inlineData(inlineData).build());
                }
            }
        }

        return GeminiChatRequest.Content.builder()
                .role(role)
                .parts(parts)
                .build();
    }

    private GeminiChatRequest.InlineData convertToInlineData(String url) {
        if (url.startsWith("data:")) {
            // 解析 Base64 data URI
            Pattern pattern = Pattern.compile("data:(image/\\w+);base64,(.*)");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                String mimeType = matcher.group(1);
                String base64Data = matcher.group(2);
                return GeminiChatRequest.InlineData.builder()
                        .mimeType(mimeType)
                        .data(base64Data)
                        .build();
            }
        }
        // TODO: 实现从http/https URL异步下载图片并转为Base64
        throw new UnsupportedOperationException("Fetching images from remote URLs is not yet implemented.");
    }

    @Override
    public ChatCompletionResult convertResponse(String providerResponse) {
        try {
            GeminiChatResponse geminiResponse = objectMapper.readValue(providerResponse, GeminiChatResponse.class);

            ChatCompletionResult result = new ChatCompletionResult();
            result.setId("gemini-" + System.currentTimeMillis());
            result.setObject("chat.completion");
            result.setCreated(System.currentTimeMillis() / 1000);

            if (geminiResponse.getCandidates() != null) {
                List<ChatCompletionResult.Choice> choices = geminiResponse.getCandidates().stream()
                    .map(candidate -> {
                        ChatCompletionResult.Choice choice = new ChatCompletionResult.Choice();
                        choice.setIndex(candidate.getIndex());
                        choice.setFinishReason(candidate.getFinishReason());

                        String combinedText = candidate.getContent().getParts().stream()
                            .map(GeminiChatResponse.Part::getText)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining());

                        ChatCompletionResult.Message message = new ChatCompletionResult.Message();
                        message.setRole("assistant");
                        message.setContent(combinedText);
                        choice.setMessage(message);

                        return choice;
                    }).collect(Collectors.toList());
                result.setChoices(choices);
            }

            // 映射 Usage
            if (geminiResponse.getUsageMetadata() != null) {
                ChatCompletionResult.Usage usage = new ChatCompletionResult.Usage();
                usage.setPromptTokens(geminiResponse.getUsageMetadata().getPromptTokenCount());
                usage.setCompletionTokens(geminiResponse.getUsageMetadata().getCandidatesTokenCount());
                usage.setTotalTokens(geminiResponse.getUsageMetadata().getTotalTokenCount());
                result.setUsage(usage);
            } else {
                 result.setUsage(new ChatCompletionResult.Usage());
            }

            return result;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse Gemini response", e);
        }
    }

    @Override
    public Flux<String> convertResponseStream(Flux<String> providerStream) {
        return providerStream
                .map(this::transformGeminiChunkToOpenAiChunk)
                .filter(Objects::nonNull)
                .concatWith(Flux.just("data: [DONE] "));
    }

    private String transformGeminiChunkToOpenAiChunk(String geminiChunk) {
        try {
            String jsonChunk = geminiChunk.startsWith("data: ") ? geminiChunk.substring(6) : geminiChunk;
            if (jsonChunk.trim().isEmpty() || !jsonChunk.trim().startsWith("{")) {
                return null;
            }

            GeminiChatResponse geminiResponse = objectMapper.readValue(jsonChunk, GeminiChatResponse.class);

            if (geminiResponse.getCandidates() == null || geminiResponse.getCandidates().isEmpty()) {
                return null;
            }

            GeminiChatResponse.Candidate candidate = geminiResponse.getCandidates().get(0);
            if (candidate.getContent() == null || candidate.getContent().getParts() == null) {
                return null;
            }

            String text = candidate.getContent().getParts().stream()
                .map(GeminiChatResponse.Part::getText)
                .filter(Objects::nonNull)
                .collect(Collectors.joining());

            ChatCompletionChunk.Delta delta = ChatCompletionChunk.Delta.builder()
                .role("assistant") // Role is often sent in stream deltas
                .content(text)
                .build();

            ChatCompletionChunk.ChunkChoice choice = ChatCompletionChunk.ChunkChoice.builder()
                .index(candidate.getIndex())
                .delta(delta)
                .finishReason(candidate.getFinishReason())
                .build();

            ChatCompletionChunk openAiChunk = ChatCompletionChunk.builder()
                .id("gemini-chunk-" + System.currentTimeMillis())
                .object("chat.completion.chunk")
                .created(System.currentTimeMillis() / 1000)
                .choices(Collections.singletonList(choice))
                .build();

            return "data: " + objectMapper.writeValueAsString(openAiChunk) + " \\/n";

        } catch (JsonProcessingException e) {
            log.error("Failed to parse Gemini stream chunk", e);
            return null;
        }
    }
}
