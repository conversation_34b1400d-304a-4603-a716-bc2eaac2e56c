{"name": "ulg-admin-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.3", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4"}, "description": "This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.", "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC"}