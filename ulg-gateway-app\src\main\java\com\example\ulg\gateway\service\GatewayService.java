package com.example.ulg.gateway.service;

import com.example.ulg.adapter.core.LlmAdapter;
import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import com.example.ulg.common.entity.LlmProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 网关核心服务
 */
@Slf4j
@Service
public class GatewayService {

    private final LlmProviderService providerService;
    private final Map<String, LlmAdapter> adapters;
    private final WebClient webClient;
    private final ObjectMapper objectMapper = new ObjectMapper();


    public GatewayService(LlmProviderService providerService, List<LlmAdapter> adapterList) {
        this.providerService = providerService;
        // 将适配器列表转换为Map，便于根据类型查找
        this.adapters = adapterList.stream()
                .collect(Collectors.toMap(LlmAdapter::getProviderType, Function.identity()));
        this.webClient = WebClient.builder().build();
    }

    /**
     * 处理非流式聊天补全请求
     * @param request OpenAI格式的请求
     * @return 响应
     */
    public Mono<ChatCompletionResult> chatCompletions(ChatCompletionRequest request) {
        // 1. 根据 model name 查找服务商配置
        LlmProvider provider = providerService.findByModelName(request.getModel())
                .orElseThrow(() -> new RuntimeException("Provider not found for model: " + request.getModel()));

        return sendRequestToProvider(provider,request);
    }
    
    /**
     * 处理流式聊天补全请求
     * @param request OpenAI格式的请求
     * @return SSE 事件流
     */
    public Flux<String> chatCompletionsStream(ChatCompletionRequest request) {
        LlmProvider provider = providerService.findByModelName(request.getModel())
                .orElseThrow(() -> new RuntimeException("Provider not found for model: " + request.getModel()));
        return sendRequestStreamToProvider(provider,request);
    }
    
    /**
     * 根据类型查找适配器
     */
    private LlmAdapter findAdapter(String providerType) {
        LlmAdapter adapter = adapters.get(providerType);
        if (adapter == null) {
            throw new RuntimeException("Adapter not found for provider type: " + providerType);
        }
        return adapter;
    }

    /**
     * 发送请求补全到服务商
     * @param provider
     * @param request
     * @return
     */
    private Mono<ChatCompletionResult> sendRequestToProvider(LlmProvider provider, ChatCompletionRequest request) {
        // 根据服务商类型和请求体，发送请求到下游
        LlmAdapter adapter = findAdapter(provider.getProviderType());
        Object providerRequest = adapter.convertRequest(request);
        Mono<ChatCompletionResult> chatCompletionResultMono;
        if ("GEMINI" .equalsIgnoreCase(provider.getProviderType())) {
            String uri = provider.getBaseUrl() + "/v1beta/models/" + request.getModel() + ":generateContent";
            chatCompletionResultMono = webClient.post()
                    .uri(uri)
                    .header("x-goog-api-key", provider.getApiKey())
                    .bodyValue(providerRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .map(adapter::convertResponse);
        } else {
            //适配openAi
            String uri = provider.getBaseUrl() + "/v1/chat/completions";
            chatCompletionResultMono = webClient.post()
                    .uri(uri) // Gemini流式URL
                    .header("Authorization", "Bearer " + provider.getApiKey())
                    .bodyValue(providerRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .map(adapter::convertResponse);
        }
        // 这里可以使用WebClient或HttpClient等工具
        return chatCompletionResultMono;
    }

    private Flux<String> sendRequestStreamToProvider(LlmProvider provider, ChatCompletionRequest request) {
        // 根据服务商类型和请求体，发送请求到下游
        LlmAdapter adapter = findAdapter(provider.getProviderType());
        Object providerRequest = adapter.convertRequest(request);
        Flux<String> providerStream;
        if ("GEMINI" .equalsIgnoreCase(provider.getProviderType())) {
            String uri = provider.getBaseUrl() + "/v1beta/models/" + request.getModel() + ":streamGenerateContent"; // Gemini流式URL
            providerStream = webClient.post()
                    .uri(uri)
                    .header("x-goog-api-key", provider.getApiKey())
                    .bodyValue(providerRequest)
                    .retrieve()
                    .bodyToFlux(String.class);
        } else {
            //适配openAi
            String uri = provider.getBaseUrl() + "/v1/chat/completions";
             providerStream = webClient.post()
                    .uri(uri)
                    .header("Authorization", "Bearer " + provider.getApiKey())
                    .bodyValue(providerRequest)
                    .retrieve()
                    .bodyToFlux(String.class);
        }
        // 这里可以使用WebClient或HttpClient等工具
        return adapter.convertResponseStream(providerStream);
    }
}
