package com.example.ulg.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;

/**
 * 启动类
 */
@SpringBootApplication(scanBasePackages = {
        "com.example.ulg.gateway",
        "com.example.ulg.adapter.gemini",
        "com.example.ulg.adapter.openai"
})
@EntityScan(basePackages = {"com.example.ulg.common.entity", "com.example.ulg.gateway.repository"})
public class UlgGatewayAppApplication {

    public static void main(String[] args) {
        SpringApplication.run(UlgGatewayAppApplication.class, args);
    }

}
