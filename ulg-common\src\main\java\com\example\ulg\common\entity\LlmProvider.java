package com.example.ulg.common.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * LLM 服务提供商实体类
 * 对应数据库中的 `llm_provider` 表
 */
@Data
@Entity
@Table(name = "llm_provider", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"model_name"})
})
public class LlmProvider {

    /**
     * 唯一标识符
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 提供商的自定义名称，用于UI展示
     */
    @Column(name = "provider_name", nullable = false, length = 100)
    private String providerName;

    /**
     * 提供商类型 (e.g., 'GEMINI', 'OPENAI')
     */
    @Column(name = "provider_type", nullable = false, length = 50)
    private String providerType;

    /**
     * 模型的唯一标识，用于API请求路由
     */
    @Column(name = "model_name", nullable = false, unique = true, length = 100)
    private String modelName;

    /**
     * 加密后的API密钥
     */
    @Column(name = "api_key", nullable = false, length = 512)
    private String apiKey;

    /**
     * API的Base URL
     */
    @Column(name = "base_url", nullable = false, length = 255)
    private String baseUrl;
    
    /**
     * 请求超时时间（毫秒）
     */
    @Column(name = "timeout_ms")
    private Integer timeoutMs;

    /**
     * 备注信息
     */
    @Lob //对于TEXT类型，使用@Lob
    @Column(name = "notes")
    private String notes;
    
    /**
     * 备用提供商ID
     */
    @Column(name = "fallback_provider_id")
    private Long fallbackProviderId;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
