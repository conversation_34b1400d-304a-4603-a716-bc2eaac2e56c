package com.example.ulg.adapter.openai;

import com.example.ulg.adapter.core.LlmAdapter;
import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
@RequiredArgsConstructor
@Slf4j
public class OpenAiAdapter implements LlmAdapter {

    private final ObjectMapper objectMapper;
    private static final String PROVIDER_TYPE = "OPENAI";

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    @Override
    public Object convertRequest(ChatCompletionRequest request) {
        // 对于OpenAI兼容的API，请求已经是正确的格式，直接透传
        return request;
    }

    @Override
    public ChatCompletionResult convertResponse(String providerResponse) {
        try {
            // 直接将下游返回的JSON反序列化为标准结果对象
            return objectMapper.readValue(providerResponse, ChatCompletionResult.class);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse OpenAI compatible response", e);
            throw new RuntimeException("Failed to parse provider response", e);
        }
    }

    @Override
    public Flux<String> convertResponseStream(Flux<String> providerStream) {
        // 流式响应的格式 (data: {...}) 已经是兼容的，直接透传
        return providerStream;
    }
}
