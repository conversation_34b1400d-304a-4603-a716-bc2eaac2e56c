package com.example.ulg.adapter.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

 @Data
 public class ChatCompletionRequest {
 
     private String model;
     private List<Message> messages;
     private Double temperature;
     @JsonProperty("top_p")
     private Double topP;
     private Integer n;
     private Boolean stream;
     private List<String> stop;
     @JsonProperty("max_tokens")
     private Integer maxTokens;
     private Integer seed;
 
     @Data
     public static class Message {
         private String role;
         // 将 content 从 String 修改为 Object 以支持多模态
         private Object content;
 
         // Helper to get content as a simple string, useful for text-only models
         public String getContentAsString() {
             if (content instanceof String) {
                 return (String) content;
             }
             if (content instanceof List) {
                 return ((List<?>) content).stream()
                         .filter(p -> p instanceof ContentPart && "text".equals(((ContentPart) p).getType()))
                         .map(p -> ((ContentPart) p).getText())
                         .findFirst()
                         .orElse("");
             }
             return null;
         }
     }
 
     @Data
     @NoArgsConstructor
     @AllArgsConstructor
     public static class ContentPart {
         private String type; // "text" or "image_url"
         private String text;
         @JsonProperty("image_url")
         private ImageUrl imageUrl;
     }
 
     @Data
     @NoArgsConstructor
     @AllArgsConstructor
     public static class ImageUrl {
         private String url;
     }
 }
