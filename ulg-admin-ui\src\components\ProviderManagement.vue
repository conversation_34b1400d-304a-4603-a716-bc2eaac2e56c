<template>
  <div class="provider-management">
    <h1>LLM Provider Management</h1>
    
    <el-button type="primary" @click="dialogVisible = true">Add Provider</el-button>

    <el-table :data="providers" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="providerName" label="Provider Name"></el-table-column>
      <el-table-column prop="providerType" label="Provider Type"></el-table-column>
      <el-table-column prop="modelName" label="Model Name"></el-table-column>
      <el-table-column prop="baseUrl" label="Base URL"></el-table-column>
      <el-table-column label="Actions">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">Edit</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">Delete</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" :title="isEdit ? 'Edit Provider' : 'Add Provider'" width="50%">
      <el-form :model="form" label-width="120px">
        <el-form-item label="Provider Name">
          <el-input v-model="form.providerName"></el-input>
        </el-form-item>
        <el-form-item label="Provider Type">
          <el-select v-model="form.providerType" placeholder="please select your provider type">
            <el-option label="GEMINI" value="GEMINI"></el-option>
            <el-option label="OPENAI" value="OPENAI"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Model Name">
          <el-input v-model="form.modelName"></el-input>
        </el-form-item>
        <el-form-item label="Base URL">
          <el-input v-model="form.baseUrl"></el-input>
        </el-form-item>
        <el-form-item label="API Key">
          <el-input v-model="form.apiKey" type="password"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="handleSubmit">Confirm</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';

const providers = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const form = ref({
  id: null,
  providerName: '',
  providerType: '',
  modelName: '',
  baseUrl: '',
  apiKey: ''
});

const API_BASE_URL = 'http://localhost:8080/api/admin/providers';

const fetchProviders = async () => {
  try {
    const response = await axios.get(API_BASE_URL);
    providers.value = response.data;
  } catch (error) {
    ElMessage.error('Failed to fetch providers.');
    console.error(error);
  }
};

const handleEdit = (provider) => {
  isEdit.value = true;
  form.value = { ...provider };
  dialogVisible.value = true;
};

const handleDelete = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/${id}`);
    ElMessage.success('Provider deleted successfully.');
    fetchProviders();
  } catch (error) {
    ElMessage.error('Failed to delete provider.');
    console.error(error);
  }
};

const handleSubmit = async () => {
  try {
    if (isEdit.value) {
      await axios.put(`${API_BASE_URL}/${form.value.id}`, form.value);
      ElMessage.success('Provider updated successfully.');
    } else {
      await axios.post(API_BASE_URL, form.value);
      ElMessage.success('Provider added successfully.');
    }
    dialogVisible.value = false;
    isEdit.value = false;
    form.value = { id: null, providerName: '', providerType: '', modelName: '', baseUrl: '', apiKey: ''};
    fetchProviders();
  } catch (error) {
    ElMessage.error('Failed to save provider.');
    console.error(error);
  }
};

onMounted(() => {
  fetchProviders();
});
</script>

<style scoped>
.provider-management {
  padding: 20px;
}
</style>
