<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 定义项目坐标 -->
    <groupId>com.example</groupId>
    <artifactId>ulg-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <!-- 父模块的打包方式必须是 pom -->
    <packaging>pom</packaging>

    <name>ulg-parent</name>
    <description>大模型统一接入网关 - 父模块</description>

    <!-- 聚合所有子模块 -->
    <modules>
        <module>ulg-common</module>
        <module>ulg-adapter</module>
        <module>ulg-gateway-app</module>
    </modules>

    <properties>
        <!-- 项目属性配置 -->
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 统一管理Spring Boot版本 -->
        <spring-boot.version>3.2.5</spring-boot.version>
    </properties>

    <!--
    dependencyManagement: 用于统一管理项目中的依赖版本。
    这里定义的依赖不会被直接引入，子模块需要时需自行显式声明，但无需指定版本号。
    -->
    <dependencyManagement>
        <dependencies>
            <!-- 引入Spring Boot的依赖物料清单 (BOM) -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- 配置Spring Boot Maven插件，用于打包和运行 -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
