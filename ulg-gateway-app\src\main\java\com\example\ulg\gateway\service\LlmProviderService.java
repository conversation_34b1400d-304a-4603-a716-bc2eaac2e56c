package com.example.ulg.gateway.service;

import com.example.ulg.common.entity.LlmProvider;
import com.example.ulg.gateway.dto.LlmProviderDto;
import com.example.ulg.gateway.repository.LlmProviderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * LlmProvider 服务层
 */
@Service
@RequiredArgsConstructor
public class LlmProviderService {

    private final LlmProviderRepository providerRepository;

    @Transactional(readOnly = true)
    public List<LlmProvider> findAll() {
        return providerRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Optional<LlmProvider> findById(Long id) {
        return providerRepository.findById(id);
    }
    
    @Transactional(readOnly = true)
    public Optional<LlmProvider> findByModelName(String modelName) {
        return providerRepository.findByModelName(modelName);
    }

    @Transactional
    public LlmProvider save(LlmProviderDto dto) {
        LlmProvider provider = new LlmProvider();
        // 在实际项目中，API Key应该在这里加密
        provider.setApiKey(dto.getApiKey()); 
        provider.setBaseUrl(dto.getBaseUrl());
        provider.setModelName(dto.getModelName());
        provider.setProviderName(dto.getProviderName());
        provider.setProviderType(dto.getProviderType());
        provider.setNotes(dto.getNotes());
        return providerRepository.save(provider);
    }

    @Transactional
    public Optional<LlmProvider> update(Long id, LlmProviderDto dto) {
        return providerRepository.findById(id)
                .map(existingProvider -> {
                    // 更新字段
                    existingProvider.setProviderName(dto.getProviderName());
                    existingProvider.setProviderType(dto.getProviderType());
                    existingProvider.setApiKey(dto.getApiKey()); // 同样需要加密
                    existingProvider.setBaseUrl(dto.getBaseUrl());
                    existingProvider.setNotes(dto.getNotes());
                    // modelName 通常不应被修改
                    return providerRepository.save(existingProvider);
                });
    }

    @Transactional
    public void deleteById(Long id) {
        providerRepository.deleteById(id);
    }
}
