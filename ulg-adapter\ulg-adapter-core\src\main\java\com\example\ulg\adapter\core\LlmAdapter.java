package com.example.ulg.adapter.core;

import com.example.ulg.adapter.core.model.ChatCompletionRequest;
import com.example.ulg.adapter.core.model.ChatCompletionResult;
import reactor.core.publisher.Flux;


/**
 * LLM 适配器核心接口
 * 定义了所有大模型适配器必须遵循的统一契约。
 *
 * @param <T> 特定于服务商的请求对象类型
 * @param <U> 特定于服务商的响应对象类型
 */
public interface LlmAdapter {

    /**
     * 将标准的 OpenAI 聊天请求转换为特定服务商的请求格式。
     *
     * @param request 标准的 OpenAI ChatCompletionRequest 对象。
     * @return 转换后的特定于服务商的请求对象。
     */
    Object convertRequest(ChatCompletionRequest request);

    /**
     * 将特定服务商的响应转换为标准的 OpenAI 聊天完成结果。
     *
     * @param providerResponse 从服务商API接收到的响应体。
     * @return 转换后的标准 ChatCompletionResult 对象。
     */
    ChatCompletionResult convertResponse(String providerResponse);
    
    /**
     * 将特定服务商的流式响应（以 Server-Sent Events 形式）转换并处理为标准的 OpenAI ChatCompletionChunk 流。
     *
     * @param providerStream 从服务商API接收到的原始响应流。
     * @return 一个 Flux<String>，每个元素都是一个符合OpenAI流式规范的JSON字符串。
     */
    Flux<String> convertResponseStream(Flux<String> providerStream);

    /**
     * 获取适配器支持的服务商类型。
     *
     * @return 代表服务商类型的字符串 (e.g., "GEMINI", "OPENAI").
     */
    String getProviderType();
}
