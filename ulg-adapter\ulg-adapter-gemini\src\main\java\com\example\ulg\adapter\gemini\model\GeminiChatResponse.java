package com.example.ulg.adapter.gemini.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GeminiChatResponse {

    private List<Candidate> candidates;
    
    @JsonProperty("usage_metadata")
    private UsageMetadata usageMetadata;

    @Data
    public static class Candidate {
        private Content content;
        private String finishReason;
        private int index;
        private List<SafetyRating> safetyRatings;
    }

    @Data
    public static class Content {
        private List<Part> parts;
        private String role;
    }

    @Data
    public static class Part {
        private String text;
    }
    
    @Data
    public static class SafetyRating {
        private String category;
        private String probability;
    }

    @Data
    public static class UsageMetadata {
        @JsonProperty("prompt_token_count")
        private int promptTokenCount;
        @JsonProperty("candidates_token_count")
        private int candidatesTokenCount;
        @JsonProperty("total_token_count")
        private int totalTokenCount;
    }
}
