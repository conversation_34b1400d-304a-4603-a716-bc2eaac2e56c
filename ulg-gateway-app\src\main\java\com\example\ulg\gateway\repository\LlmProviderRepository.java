package com.example.ulg.gateway.repository;

import com.example.ulg.common.entity.LlmProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * LlmProvider 数据访问接口
 */
@Repository
public interface LlmProviderRepository extends JpaRepository<LlmProvider, Long> {

    /**
     * 根据模型名称查找服务商配置。
     * 模型名称是唯一的，因此返回 Optional<LlmProvider>。
     *
     * @param modelName 模型的唯一标识符
     * @return 包含找到的服务商配置的 Optional，如果未找到则为空
     */
    Optional<LlmProvider> findByModelName(String modelName);
}
