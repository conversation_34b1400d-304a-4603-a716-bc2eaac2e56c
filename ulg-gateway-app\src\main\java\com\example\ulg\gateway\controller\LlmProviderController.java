package com.example.ulg.gateway.controller;

import com.example.ulg.common.entity.LlmProvider;
import com.example.ulg.gateway.dto.LlmProviderDto;
import com.example.ulg.gateway.service.LlmProviderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 后台管理 - 服务商管理API
 */
@RestController
@RequestMapping("/api/admin/providers")
@RequiredArgsConstructor
public class LlmProviderController {

    private final LlmProviderService providerService;

    @PostMapping
    public ResponseEntity<LlmProvider> create(@RequestBody LlmProviderDto dto) {
        LlmProvider savedProvider = providerService.save(dto);
        return ResponseEntity.ok(savedProvider);
    }

    @GetMapping
    public ResponseEntity<List<LlmProvider>> getAll() {
        return ResponseEntity.ok(providerService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<LlmProvider> getById(@PathVariable Long id) {
        return providerService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    public ResponseEntity<LlmProvider> update(@PathVariable Long id, @RequestBody LlmProviderDto dto) {
        return providerService.update(id, dto)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        providerService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
