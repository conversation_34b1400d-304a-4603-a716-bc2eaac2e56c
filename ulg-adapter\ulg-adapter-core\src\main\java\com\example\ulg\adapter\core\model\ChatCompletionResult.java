package com.example.ulg.adapter.core.model;

import lombok.Data;
import java.util.List;

/**
 * 标准的 OpenAI Chat Completion 响应模型。
 * 对应于 `POST /v1/chat/completions` 的非流式响应体。
 */
@Data
public class ChatCompletionResult {

    private String id;
    private String object;
    private long created;
    private String model;
    private List<Choice> choices;
    private Usage usage;

    @Data
    public static class Choice {
        private int index;
        private Message message;
        private String finishReason;
    }

    @Data
    public static class Message {
        private String role;
        private String content;
    }

    @Data
    public static class Usage {
        private int promptTokens;
        private int completionTokens;
        private int totalTokens;
    }
}
